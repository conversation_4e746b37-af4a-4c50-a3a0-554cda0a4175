import uuid
from decimal import Decimal

from django.core.validators import FileExtensionValidator, MinValueValidator
from django.db import models

from core.models import BaseModel, CustomUser, SoftDeleteModel, GeneralSettings, ServiceableCity
from utils.common import dish_image_upload_to, restaurant_cover_image_upload_to, payment_receipt_upload_to, \
    restaurant_municipal_license_upload_to, restaurant_fssai_license_upload_to, restaurant_pan_card_upload_to, \
    restaurant_firm_registration_document_upload_to, owner_aadhar_card_front_upload_to, \
    owner_aadhar_card_back_upload_to, owner_pan_card_upload_to
from utils.validations import validate_dial_code, validate_mobile_number, validate_ifsc_code


class Restaurant(BaseModel):
    restaurant_id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    admin = models.OneToOneField(CustomUser, on_delete=models.CASCADE, related_name="restaurant")
    code = models.Char<PERSON>ield(max_length=50, unique=True)
    name = models.CharField(max_length=100)
    description = models.TextField(null=True, blank=True)
    address_line_1 = models.Char<PERSON>ield(max_length=100, null=True, blank=True)
    address_line_2 = models.CharField(max_length=100, null=True, blank=True)
    landmark = models.CharField(max_length=100, null=True, blank=True)
    city = models.ForeignKey(ServiceableCity, on_delete=models.CASCADE, related_name="restaurants")
    state = models.CharField(max_length=100, null=True, blank=True)
    pincode = models.CharField(max_length=10, null=True, blank=True)
    restaurant_cover_image = models.ImageField(upload_to=restaurant_cover_image_upload_to, null=True, blank=True,
                                               validators=[
                                                   FileExtensionValidator(allowed_extensions=["jpeg", "jpg", "png"])])
    dial_code = models.CharField(max_length=5, default="+91", validators=[validate_dial_code])
    mobile_number = models.CharField(max_length=100, validators=[validate_mobile_number])
    email = models.EmailField(null=True, blank=True)
    website = models.URLField(null=True, blank=True)
    minimum_order_quantity = models.PositiveIntegerField(default=50)
    is_approved = models.BooleanField(default=False)

    # Restaurant Registration Details
    registration_number = models.CharField(max_length=100, null=True, blank=True)
    gst_number = models.CharField(max_length=100, null=True, blank=True)
    municipal_license = models.FileField(upload_to=restaurant_municipal_license_upload_to, null=True, blank=True,
                                         validators=[FileExtensionValidator(allowed_extensions=["pdf"])])
    fssai_license = models.FileField(upload_to=restaurant_fssai_license_upload_to, null=True, blank=True,
                                     validators=[FileExtensionValidator(allowed_extensions=["pdf"])])
    pan_card = models.FileField(upload_to=restaurant_pan_card_upload_to, null=True, blank=True,
                                validators=[FileExtensionValidator(allowed_extensions=["jpeg", "jpg", "png"])])
    firm_type = models.CharField(max_length=100, null=True, blank=True)
    firm_registration_document = models.FileField(upload_to=restaurant_firm_registration_document_upload_to,
                                                  null=True, blank=True,
                                                  validators=[FileExtensionValidator(allowed_extensions=["pdf"])])

    # Owner Details
    owner_name = models.CharField(max_length=100, null=True, blank=True)
    owner_dial_code = models.CharField(max_length=5, default="+91", validators=[validate_dial_code], null=True,
                                       blank=True)
    owner_mobile_number = models.CharField(max_length=100, validators=[validate_mobile_number], null=True, blank=True)
    owner_email = models.EmailField(null=True, blank=True)
    owner_aadhar_card_front = models.FileField(upload_to=owner_aadhar_card_front_upload_to, null=True, blank=True,
                                               validators=[
                                                   FileExtensionValidator(allowed_extensions=["jpeg", "jpg", "png"])])
    owner_aadhar_card_back = models.FileField(upload_to=owner_aadhar_card_back_upload_to, null=True, blank=True,
                                              validators=[
                                                  FileExtensionValidator(allowed_extensions=["jpeg", "jpg", "png"])])
    owner_pan_card = models.FileField(upload_to=owner_pan_card_upload_to, null=True, blank=True,
                                      validators=[FileExtensionValidator(allowed_extensions=["jpeg", "jpg", "png"])])

    # Power of Attorney Details
    power_of_attorney_name = models.CharField(max_length=100, null=True, blank=True)
    power_of_attorney_dial_code = models.CharField(max_length=5, default="+91", validators=[validate_dial_code],
                                                   null=True, blank=True)
    power_of_attorney_mobile_number = models.CharField(max_length=100, validators=[validate_mobile_number], null=True,
                                                       blank=True)
    power_of_attorney_email = models.EmailField(null=True, blank=True)

    # Point of Contact Details
    point_of_contact_name = models.CharField(max_length=100, null=True, blank=True)
    point_of_contact_dial_code = models.CharField(max_length=5, default="+91", validators=[validate_dial_code],
                                                  null=True, blank=True)
    point_of_contact_mobile_number = models.CharField(max_length=100, validators=[validate_mobile_number], null=True,
                                                      blank=True)

    class Meta:
        db_table = "restaurant"
        ordering = ["-created_at"]

    def __str__(self):
        return self.name

    def create_default_pickup_times(self):
        from datetime import datetime

        days_of_week = [day[0] for day in RestaurantPickUpTime.DAY_OF_WEEK_CHOICES]

        # TODO: Pick the default time that has been set by platform admin in settings (start time + end time)
        start_time = GeneralSettings.objects.get(code=GeneralSettings.RESTAURANT_DEFAULT_PICKUP_TIME).json_value.get(
            "start_time", "12:00")
        end_time = GeneralSettings.objects.get(code=GeneralSettings.RESTAURANT_DEFAULT_PICKUP_TIME).json_value.get(
            "end_time", "12:30")

        pickup_times = []
        for day in days_of_week:
            pickup_times.append(RestaurantPickUpTime(
                restaurant=self,
                day_of_week=day,
                # convert start_time and end_time to datetime.time objects
                start_time=datetime.strptime(start_time, "%H:%M").time(),
                end_time=datetime.strptime(end_time, "%H:%M").time()
            ))

        RestaurantPickUpTime.objects.bulk_create(pickup_times)


class RestaurantBankAccount(BaseModel):
    # Bank account types
    SAVING = "saving"
    CURRENT = "current"

    ACCOUNT_TYPE_CHOICES = [
        (SAVING, "Saving"),
        (CURRENT, "Current"),
    ]

    account_id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    restaurant = models.ForeignKey(Restaurant, on_delete=models.CASCADE, related_name="bank_accounts")
    is_primary = models.BooleanField(default=False)
    bank_name = models.CharField(max_length=100)
    account_holder_name = models.CharField(max_length=100)
    account_type = models.CharField(max_length=100, choices=ACCOUNT_TYPE_CHOICES)
    account_number = models.CharField(max_length=50)
    ifsc_code = models.CharField(max_length=50, validators=[validate_ifsc_code])

    class Meta:
        db_table = "restaurant_bank_account"
        ordering = ["-created_at"]
        unique_together = ('restaurant', 'account_number', 'is_primary')

    def __str__(self):
        return f"{self.restaurant.name} - {self.account_number}"


class RestaurantEmployee(BaseModel):
    employee_id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    restaurant = models.ForeignKey(Restaurant, on_delete=models.CASCADE, related_name="employees")
    user = models.OneToOneField(CustomUser, on_delete=models.CASCADE, related_name="restaurant_employee")

    class Meta:
        db_table = "restaurant_employee"
        ordering = ["-created_at"]

    def __str__(self):
        return f"{self.restaurant.name} - {self.user.get_full_name()}"


class CuisineCategory(BaseModel, SoftDeleteModel):
    cuisine_category_id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    code = models.CharField(max_length=100, unique=True)
    name = models.CharField(max_length=100)
    description = models.TextField(null=True, blank=True)
    order = models.PositiveIntegerField(default=0)
    is_active = models.BooleanField(default=True)

    class Meta:
        db_table = "cuisine_category"
        ordering = ["order", "-created_at"]

    def save(self, *args, **kwargs):
        self.code = self.name.lower().replace(' ', '_')
        super().save(*args, **kwargs)

    def __str__(self):
        return self.name


class RestaurantMenuItem(BaseModel):
    # Dish types
    VEG = "veg"
    NON_VEG = "non_veg"

    DISH_TYPE_CHOICES = [
        (VEG, "Vegetarian"),
        (NON_VEG, "Non-Vegetarian")
    ]

    menu_item_id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    restaurant = models.ForeignKey(Restaurant, on_delete=models.CASCADE, related_name="menu")
    cuisine_category = models.ForeignKey(CuisineCategory, on_delete=models.CASCADE, related_name="menu_items")
    dish_type = models.CharField(max_length=100, choices=DISH_TYPE_CHOICES)
    dish_name = models.CharField(max_length=100)
    dish_description = models.TextField(null=True, blank=True)
    restaurant_price = models.DecimalField(max_digits=10, decimal_places=2,
                                           validators=[MinValueValidator(Decimal('0.00'))])
    platform_price = models.DecimalField(max_digits=10, decimal_places=2, default=0.00,
                                         validators=[MinValueValidator(Decimal('0.00'))])
    is_approved = models.BooleanField(default=False)

    class Meta:
        db_table = "restaurant_menu_item"
        ordering = ["-created_at"]

    def __str__(self):
        return f"{self.restaurant.name} - {self.dish_name}"

    def delete(self, *args, **kwargs):
        for image in self.images.all():
            image.delete()
        super().delete(*args, **kwargs)


class RestaurantMenuItemImage(BaseModel):
    menu_item_image_id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    menu_item = models.ForeignKey(RestaurantMenuItem, on_delete=models.CASCADE, related_name="images")
    image = models.ImageField(upload_to=dish_image_upload_to,
                              validators=[FileExtensionValidator(allowed_extensions=["jpeg", "jpg", "png"])])
    order = models.PositiveIntegerField(default=0)

    class Meta:
        db_table = "restaurant_menu_item_image"
        ordering = ["order", "-created_at"]


class RestaurantPickUpTime(BaseModel):
    # Days of the week
    MONDAY = "monday"
    TUESDAY = "tuesday"
    WEDNESDAY = "wednesday"
    THURSDAY = "thursday"
    FRIDAY = "friday"
    SATURDAY = "saturday"
    SUNDAY = "sunday"

    DAY_OF_WEEK_CHOICES = [
        (MONDAY, "Monday"),
        (TUESDAY, "Tuesday"),
        (WEDNESDAY, "Wednesday"),
        (THURSDAY, "Thursday"),
        (FRIDAY, "Friday"),
        (SATURDAY, "Saturday"),
        (SUNDAY, "Sunday"),
    ]

    pickup_time_id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    restaurant = models.ForeignKey(Restaurant, on_delete=models.CASCADE, related_name="pickup_times")
    day_of_week = models.CharField(max_length=10, choices=DAY_OF_WEEK_CHOICES)
    start_time = models.TimeField(help_text="Start time of the pickup window (format: HH:MM)")
    end_time = models.TimeField(help_text="End time of the pickup window (format: HH:MM)")

    class Meta:
        db_table = "restaurant_pickup_time"
        ordering = ["created_at"]
        unique_together = ("restaurant", "day_of_week")

    def __str__(self):
        return f"{self.restaurant.name} - {self.start_time} to {self.end_time}"


class RestaurantPaymentLogs(BaseModel, SoftDeleteModel):
    # Payment methods
    BANK_TRANSFER = "bank_transfer"
    CASH = "cash"
    UPI = "upi"

    PAYMENT_METHOD_CHOICES = [
        (BANK_TRANSFER, "Bank Transfer"),
        (CASH, "Cash"),
        (UPI, "UPI"),
    ]

    payment_log_id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    payment_ref_id = models.CharField(max_length=20, unique=True)
    restaurant = models.ForeignKey(Restaurant, on_delete=models.CASCADE, related_name="payment_logs")
    restaurant_bank_account = models.ForeignKey(RestaurantBankAccount, on_delete=models.SET_NULL, null=True,
                                                limit_choices_to={"is_primary": True}, related_name="payment_logs")
    amount = models.DecimalField(max_digits=10, decimal_places=2)
    payment_method = models.CharField(max_length=50, choices=PAYMENT_METHOD_CHOICES)
    payment_receipt = models.FileField(upload_to=payment_receipt_upload_to, null=True, blank=True,
                                       validators=[FileExtensionValidator(allowed_extensions=["pdf"])])
    payment_remarks = models.TextField(null=True, blank=True)

    class Meta:
        db_table = "restaurant_payment_logs"
        ordering = ["-created_at"]

    def __str__(self):
        return f"{self.restaurant.name} - {self.amount}"
