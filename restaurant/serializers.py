from django.contrib.auth import get_user_model, authenticate
from rest_framework import serializers
from django.contrib.auth.hashers import make_password
from django.utils import timezone
from rest_framework_simplejwt.tokens import RefreshToken
from django.shortcuts import get_object_or_404

from core.serializers import CustomUserProfileSerializer
from core.utils import verify_otp, generate_and_send_otp
from rbac_system.models import CustomGroup
from rbac_system.utils import restaurant_admin_group, is_group_allowed_for_restaurant_employee
from restaurant.models import Restaurant, RestaurantBankAccount, RestaurantEmployee, CuisineCategory, \
    RestaurantMenuItem, RestaurantPickUpTime, RestaurantPaymentLogs, RestaurantMenuItemImage
from utils.common import unique_restaurant_admin, calculate_time_difference_minutes, generate_strong_password, \
    move_temp_file_to_permanent_location, dish_image_upload_to
from utils.constants import GROUP_CODE_RESTAURANT_ADMIN, RESTAURANT_PICKUP_TIME_MINIMUM_DURATION_MINUTES

User = get_user_model()


class RestaurantSignupRequestSerializer(serializers.ModelSerializer):
    """Serializer for consumer signup process."""
    restaurant_name = serializers.CharField(required=True, write_only=True)
    terms_accepted = serializers.BooleanField(required=True, write_only=True)

    class Meta:
        model = User
        fields = ('restaurant_name', 'first_name', 'last_name', 'primary_dial_code', 'primary_mobile_number',
                  'secondary_dial_code', 'secondary_mobile_number', 'email', 'password', 'terms_accepted')
        extra_kwargs = {
            'restaurant_name': {'required': True},
            'first_name': {'required': True},
            'last_name': {'required': True},
            'email': {'required': True},
            'password': {'write_only': True},
            'primary_dial_code': {'required': True},
            'primary_mobile_number': {'required': True},
            'secondary_dial_code': {'required': True},
            'secondary_mobile_number': {'required': True},
        }

    def validate(self, attrs):
        # Validate terms acceptance
        if not attrs.get('terms_accepted'):
            raise serializers.ValidationError("You must accept the terms and conditions.")

        # Check if restaurant admin already exists
        if not unique_restaurant_admin(
                dial_code=attrs['primary_dial_code'],
                mobile_number=attrs['primary_mobile_number'],
                email=attrs['email']
        ):
            raise serializers.ValidationError("A restaurant Admin with this mobile number already exists.")

        return attrs

    def validate_password(self, value):
        return make_password(value)

    def create(self, validated_data):
        # Remove non-model fields
        validated_data.pop('terms_accepted')
        restaurant_name = validated_data.pop('restaurant_name')
        user = super().create(validated_data)

        if user:
            user.groups.add(restaurant_admin_group())
            # Create restaurant
            Restaurant.objects.create(
                admin=user,
                name=restaurant_name,
                mobile_number=user.primary_mobile_number,
                email=user.email
            )

            success, message_or_error, otp_object = generate_and_send_otp(user.user_id)
            if not success:
                raise serializers.ValidationError(message_or_error)

        return user


class RestaurantSignUpVerifySerializer(serializers.Serializer):
    """Serializer for restaurant signup verification (step 2 - verify OTP)."""
    dial_code = serializers.CharField(required=True)
    mobile_number = serializers.CharField(required=True)
    otp = serializers.IntegerField(required=True)

    def validate(self, attrs):
        dial_code = attrs.get('dial_code')
        mobile_number = attrs.get('mobile_number')
        otp = attrs.get('otp')

        user = get_object_or_404(
            User,
            primary_dial_code=dial_code,
            primary_mobile_number=mobile_number,
            groups=restaurant_admin_group(),
            is_deleted=False
        )

        if not user.is_active:
            raise serializers.ValidationError("This account is inactive.")

        verified = verify_otp(dial_code, mobile_number, otp)

        if not verified:
            raise serializers.ValidationError("Invalid OTP.")

        attrs['user'] = user
        return attrs


class RestaurantLoginRequestSerializer(serializers.Serializer):
    """Serializer for restaurant admin login request (step 1 - request OTP)."""
    dial_code = serializers.CharField(required=True)
    mobile_number = serializers.CharField(required=True)

    def validate(self, attrs):
        dial_code = attrs.get('dial_code')
        mobile_number = attrs.get('mobile_number')

        user = User.objects.filter(
            primary_dial_code=dial_code,
            primary_mobile_number=mobile_number,
            groups=restaurant_admin_group(),
            is_deleted=False
        ).first()

        if not user:
            raise serializers.ValidationError("No restaurant admin account found with this mobile number.")
        if not user.mobile_verified:
            raise serializers.ValidationError(
                "Account with this mobile number is not verified. Kindly verify the account.")
        if not user.is_active:
            raise serializers.ValidationError("This account is inactive. Please contact support.")

        attrs['user'] = user
        return attrs

    def create(self, validated_data):
        user = validated_data.get('user')
        success, error, otp_object = generate_and_send_otp(user.user_id)
        if not success:
            raise serializers.ValidationError(error)
        return validated_data


class RestaurantLoginVerifySerializer(serializers.Serializer):
    """Serializer for restaurant admin login verification (step 2 - verify OTP)."""
    dial_code = serializers.CharField(required=True)
    mobile_number = serializers.CharField(required=True)
    otp = serializers.IntegerField(required=True)

    def validate(self, attrs):
        dial_code = attrs.get('dial_code')
        mobile_number = attrs.get('mobile_number')
        otp = attrs.get('otp')

        user = get_object_or_404(
            User,
            primary_dial_code=dial_code,
            primary_mobile_number=mobile_number,
            groups=restaurant_admin_group(),
            is_deleted=False
        )

        if not user.is_active:
            raise serializers.ValidationError("This account is inactive.")

        verified = verify_otp(dial_code, mobile_number, otp)
        if not verified:
            raise serializers.ValidationError("Invalid OTP.")

        attrs['user'] = user
        return attrs


class RestaurantLoginSerializer(serializers.Serializer):
    """Serializer for restaurant login with email and password."""
    dial_code = serializers.CharField(required=True)
    mobile_number = serializers.CharField(required=True)
    password = serializers.CharField(required=True, write_only=True)

    def validate(self, attrs):
        dial_code = attrs.get('dial_code')
        mobile_number = attrs.get('mobile_number')
        password = attrs.get('password')

        user = authenticate(username=mobile_number, password=password)
        if (not user) or user.is_deleted or (not user.is_in_group(GROUP_CODE_RESTAURANT_ADMIN)):
            raise serializers.ValidationError("Invalid credentials.")
        if not user.mobile_verified:
            raise serializers.ValidationError("Please verify your mobile number.")
        if not (user.is_active and user.restaurant.is_active):
            raise serializers.ValidationError("This account is inactive.")
        if not user.is_in_group(GROUP_CODE_RESTAURANT_ADMIN):
            raise serializers.ValidationError("You do not have permission to access this resource.")

        attrs['user'] = user
        return attrs

    def create(self, validated_data):
        user = validated_data.get('user')

        user.last_login = timezone.now()
        user.save(update_fields=["last_login"])

        login_data = {
            "user_id": user.user_id,
            "restaurant_name": user.restaurant.name,
            "email": user.email,
            "is_approved": user.restaurant.is_approved,
        }

        if user.restaurant.is_approved:
            refresh = RefreshToken.for_user(user)
            access_token = str(refresh.access_token)

            login_data.update({
                "access": access_token,
                "refresh": str(refresh),
            })

        return login_data


class RestaurantProfileSerializer(serializers.ModelSerializer):
    """Serializer for restaurant profile."""

    class Meta:
        model = Restaurant
        fields = ['restaurant_id', 'admin', 'name', 'email',  'dial_code', 'mobile_number',
                  'restaurant_cover_image', 'website', 'is_approved', 'is_active', 'created_at', 'updated_at']
        read_only_fields = ['restaurant_id', 'admin', 'code', 'is_approved', 'is_active', 'primary_dial_code',
                            'primary_mobile_number', 'created_at', 'updated_at']


class RestaurantBankAccountSerializer(serializers.ModelSerializer):
    """Serializer for restaurant bank account."""

    class Meta:
        model = RestaurantBankAccount
        fields = ['account_id', 'bank_name', 'account_holder_name', 'account_type', 'account_number', 'ifsc_code',
                  'is_primary', 'created_at', 'updated_at']
        read_only_fields = ['account_id', 'is_primary', 'created_at', 'updated_at']


class RestaurantEmployeeSerializer(serializers.ModelSerializer):
    """Serializer for restaurant employees."""
    user = CustomUserProfileSerializer()

    class Meta:
        model = RestaurantEmployee
        fields = ['employee_id', 'user', 'created_at', 'updated_at']
        read_only_fields = ['employee_id', 'created_at', 'updated_at']

    def validate(self, attrs):
        user = attrs.get('user')
        restaurant = attrs.get('restaurant')

        if user.restaurant_employee.exists():
            raise serializers.ValidationError("This user is already an employee of another restaurant.")

        if restaurant.employees.filter(user=user).exists():
            raise serializers.ValidationError("This user is already an employee of this restaurant.")

        return attrs


class RestaurantEmployeeCreateSerializer(serializers.ModelSerializer):
    """Serializer for creating restaurant employees."""
    first_name = serializers.CharField(required=True, write_only=True)
    last_name = serializers.CharField(required=True, write_only=True)
    primary_dial_code = serializers.CharField(required=True, write_only=True)
    primary_mobile_number = serializers.CharField(required=True, write_only=True)
    email = serializers.EmailField(required=True, write_only=True)
    group_id = serializers.IntegerField(required=True, write_only=True)

    class Meta:
        model = RestaurantEmployee
        fields = ['employee_id', 'first_name', 'last_name', 'primary_dial_code',
                  'primary_mobile_number', 'email', 'group_id', 'created_at', 'updated_at']
        read_only_fields = ['employee_id', 'created_at', 'updated_at']

    def validate(self, attrs):
        """Validate employee data."""
        # Check if user with this mobile number already exists
        primary_dial_code = attrs.get('primary_dial_code')
        primary_mobile_number = attrs.get('primary_mobile_number')
        email = attrs.get('email')
        group_id = attrs.get('group_id')

        if User.objects.filter(primary_dial_code=primary_dial_code,
                               primary_mobile_number=primary_mobile_number).exists():
            raise serializers.ValidationError("A user with this mobile number already exists.")

        if User.objects.filter(email=email).exists():
            raise serializers.ValidationError("A user with this email already exists.")

        if not is_group_allowed_for_restaurant_employee(group_id):
            raise serializers.ValidationError({
                "group_id": "This group cannot be assigned to restaurant employees. "
                            "Only Shift Manager, Chef, or other allowed roles can be assigned."
            })

        return attrs

    def create(self, validated_data):
        """Create a new user and restaurant employee."""
        # Extract user data
        first_name = validated_data.pop('first_name')
        last_name = validated_data.pop('last_name')
        primary_dial_code = validated_data.pop('primary_dial_code')
        primary_mobile_number = validated_data.pop('primary_mobile_number')
        email = validated_data.pop('email')
        group_id = validated_data.pop('group_id')
        restaurant = validated_data.get('restaurant')

        # Generate a random password
        password = generate_strong_password()

        # Create user
        user = User.objects.create(
            first_name=first_name,
            last_name=last_name,
            primary_dial_code=primary_dial_code,
            primary_mobile_number=primary_mobile_number,
            email=email,
            password=make_password(password),
            is_active=True
        )
        print(
            f"-----> Restaurant Employee created: {user.user_id} - {user.get_full_name()} with password {password} <-----")

        # Assign the group to the user
        try:
            group = CustomGroup.objects.get(id=group_id)
            user.groups.add(group)
        except CustomGroup.DoesNotExist:
            # This shouldn't happen due to validation, but just in case
            pass

        # Create restaurant employee
        employee = RestaurantEmployee.objects.create(
            user=user,
            restaurant=restaurant
        )

        # TODO: Implement welcome email for employees
        # Send welcome email
        # welcome_email_sent = send_notification.delay(
        #     user_id=user.user_id,
        #     notification_type=NotificationType.EMAIL.value,
        #     category=NotificationCategory.GENERAL.value,
        #     template_code=NotificationTemplateCode.EMPLOYEE_WELCOME.value,
        #     title="Welcome to Hungersate",
        #     message={
        #         'recipient_first_name': user.first_name,
        #         'recipient_last_name': user.last_name,
        #         'restaurant_name': restaurant.name,
        #         'employee_role': group.name,
        #         'employee_dial_code': user.primary_dial_code,
        #         'employee_mobile_number': user.primary_mobile_number,
        #         'employee_password': password,
        #         'employee_login_url': 'https://127.0.0.1:8000/restaurant-admin/auth/login/',
        #     }
        # )

        return employee


class RestaurantCuisineCategorySerializer(serializers.ModelSerializer):
    """Serializer for restaurant cuisine categories."""

    class Meta:
        model = CuisineCategory
        fields = ['cuisine_category_id', 'name', 'code', 'description']
        read_only_fields = ['cuisine_category_id']


class RestaurantMenuItemImageSerializer(serializers.ModelSerializer):
    """Serializer for restaurant menu item images."""

    class Meta:
        model = RestaurantMenuItemImage
        fields = ['menu_item_image_id', 'image', 'order']
        read_only_fields = ['menu_item_image_id']


class RestaurantMenuSerializer(serializers.ModelSerializer):
    """Serializer for restaurant menu items."""
    cuisine_category_name = serializers.CharField(source='cuisine_category.name', read_only=True)
    images = RestaurantMenuItemImageSerializer(many=True, read_only=True)

    class Meta:
        model = RestaurantMenuItem
        fields = [
            'menu_item_id', 'restaurant', 'cuisine_category', 'cuisine_category_name', 'dish_type', 'dish_name',
            'dish_description', 'images', 'restaurant_price', 'is_active', 'is_approved', 'created_at', 'updated_at'
        ]
        read_only_fields = ['menu_item_id', 'restaurant', 'is_approved', 'created_at', 'updated_at']


class RestaurantMenuCreateUpdateSerializer(serializers.ModelSerializer):
    """Serializer for creating restaurant menu items."""
    # add a list of images with maximum 10 images
    image_urls = serializers.ListField(
        child=serializers.URLField(),
        write_only=True,
        required=False,
        max_length=10
    )

    class Meta:
        model = RestaurantMenuItem
        fields = [
            'menu_item_id', 'cuisine_category', 'dish_type', 'dish_name', 'dish_description', 'image_urls',
            'restaurant_price', 'created_at', 'updated_at'
        ]
        read_only_fields = ['menu_item_id', 'is_approved', 'created_at', 'updated_at']

    def create(self, validated_data):
        restaurant_admin = self.context['request'].user

        if not restaurant_admin.is_in_group(GROUP_CODE_RESTAURANT_ADMIN):
            raise serializers.ValidationError(
                "You cannot add menu items. Please ask Restaurant Admin to add menu items.")

        validated_data['restaurant'] = restaurant_admin.restaurant
        validated_data['is_approved'] = False
        image_urls = validated_data.pop('image_urls', [])
        menu_item = super().create(validated_data)

        # Process image URLs
        for index, image_url in enumerate(image_urls):
            # Create a new image instance
            image_instance = RestaurantMenuItemImage(
                menu_item=menu_item,
                order=index + 1
            )

            # Move the file from temp location to final destination using the model's upload_to function
            destination_path = move_temp_file_to_permanent_location(
                image_url,
                dish_image_upload_to,
                image_instance
            )

            if destination_path:
                image_instance.image = destination_path
                image_instance.save()

        return menu_item

    def update(self, instance, validated_data):
        image_urls = validated_data.pop('image_urls', [])
        delete_images = validated_data.pop('delete_images', [])

        # Update menu item
        instance = super().update(instance, validated_data)

        # Delete specified images
        if delete_images:
            RestaurantMenuItemImage.objects.filter(
                menu_item=instance,
                menu_item_image_id__in=delete_images
            ).delete()

        # Add new images
        if image_urls:
            for index, image_url in enumerate(image_urls):
                image_instance = RestaurantMenuItemImage(
                    menu_item=instance,
                    order=instance.images.count() + index + 1
                )

                destination_path = move_temp_file_to_permanent_location(
                    image_url,
                    dish_image_upload_to,
                    image_instance
                )

                if destination_path:
                    image_instance.image = destination_path
                    image_instance.save()

        return instance


class RestaurantPickUpTimeSerializer(serializers.ModelSerializer):
    """Serializer for restaurant pickup times."""

    class Meta:
        model = RestaurantPickUpTime
        fields = ['pickup_time_id', 'day_of_week', 'start_time', 'end_time', 'created_at', 'updated_at']
        read_only_fields = ['pickup_time_id', 'created_at', 'updated_at']

    def validate(self, attrs):
        """Validate that start_time is before end_time."""
        start_time = attrs.get('start_time')
        end_time = attrs.get('end_time')
        if start_time and end_time and start_time >= end_time:
            raise serializers.ValidationError("End time must be after start time.")

        time_difference = calculate_time_difference_minutes(start_time, end_time)
        if time_difference != RESTAURANT_PICKUP_TIME_MINIMUM_DURATION_MINUTES:
            raise serializers.ValidationError(
                f"Pickup time duration must be exactly {RESTAURANT_PICKUP_TIME_MINIMUM_DURATION_MINUTES} minutes.")

        return attrs


class RestaurantPaymentLogSerializer(serializers.ModelSerializer):
    """Serializer for restaurant payment logs."""
    bank_account_number = serializers.CharField(source='restaurant_bank_account.account_number', read_only=True)

    class Meta:
        model = RestaurantPaymentLogs
        fields = [
            'payment_log_id', 'payment_ref_id', 'restaurant_bank_account', 'bank_account_number', 'amount',
            'payment_method', 'payment_receipt', 'payment_remarks', 'created_at'
        ]
        read_only_fields = ['payment_log_id', 'payment_ref_id', 'created_at']
