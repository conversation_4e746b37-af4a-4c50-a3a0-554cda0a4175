#!/bin/bash
set -e

echo "🔄 Running Django management commands..."
python manage.py migrate
echo "✅ Migration completed."

echo "🔄 Running management commands..."
python manage.py configure_groups_and_permissions
python manage.py configure_settings
python manage.py load_cuisine_categories
python manage.py configure_notification_settings
python manage.py configure_notification_templates
python manage.py check_razorpay_connection
python manage.py configure_restaurants_and_menus
python manage.py configure_serviceable_locations
echo "✅ Management commands completed."

echo "🔍 Checking uwsgi location:"
which uwsgi || echo "❌ uwsgi not found!"

echo "✅ Running uWSGI..."
uwsgi --ini /hungersate/.docker/uwsgi_config.ini &

echo "✅ Starting supervisord..."
exec /usr/bin/supervisord -c /etc/supervisor/supervisord.conf
