from django.contrib.auth.hashers import make_password
from rest_framework import serializers
from django.contrib.auth import get_user_model, authenticate
from django.contrib.auth.password_validation import validate_password
from django.utils import timezone

from core.models import StaticPages, ServiceableState, ServiceableCity
from core.utils import generate_restaurant_code
from rbac_system.utils import restaurant_admin_group
from restaurant.models import Restaurant, RestaurantMenuItem, RestaurantPaymentLogs, RestaurantMenuItemImage, \
    RestaurantPickUpTime, RestaurantBankAccount, CuisineCategory
from utils.common import unique_restaurant_admin, generate_strong_password
from utils.constants import GROUP_CODE_PLATFORM_ADMIN

User = get_user_model()


class ForgotPasswordSerializer(serializers.Serializer):
    """Serializer for forgot password request."""
    dial_code = serializers.CharField(required=True)
    mobile_number = serializers.CharField(required=True)

    def validate_mobile_number(self, value):
        if not User.objects.filter(primary_mobile_number=value).exists():
            raise serializers.ValidationError("No account found with this mobile number.")
        return value


class ChangePasswordSerializer(serializers.Serializer):
    """Serializer for changing password."""
    old_password = serializers.CharField(required=True)
    new_password = serializers.CharField(required=True, validators=[validate_password])

    def validate(self, attrs):
        if attrs['old_password'] == attrs['new_password']:
            raise serializers.ValidationError("New password cannot be the same as the old password.")
        return attrs

    def validate_old_password(self, value):
        """Validate that the old password is correct."""
        user = self.context['request'].user
        if not user.check_password(value):
            raise serializers.ValidationError("Old password is incorrect!")
        return value


class ResetPasswordSerializer(serializers.Serializer):
    """Serializer for password reset."""
    dial_code = serializers.CharField(required=True)
    mobile_number = serializers.CharField(required=True)
    otp = serializers.CharField(required=True)
    new_password = serializers.CharField(required=True, validators=[validate_password])


class ResendOTPSerializer(serializers.Serializer):
    """Serializer for resending OTP."""
    dial_code = serializers.CharField(required=True)
    mobile_number = serializers.CharField(required=True)


class AdminLoginSerializer(serializers.Serializer):
    """Serializer for platform admin login with phone number and password."""
    dial_code = serializers.CharField(required=True)
    mobile_number = serializers.CharField(required=True)
    password = serializers.CharField(required=True, write_only=True)

    def validate(self, attrs):
        dial_code = attrs.get('dial_code')
        mobile_number = attrs.get('mobile_number')
        password = attrs.get('password')

        # Authenticate user
        user = authenticate(username=mobile_number, password=password)

        if not user:
            raise serializers.ValidationError("Invalid credentials.")

        if user.is_deleted:
            raise serializers.ValidationError("This account has been deleted.")

        if not user.is_active:
            raise serializers.ValidationError("This account is inactive.")

        # Check if user is a platform admin
        if not user.is_in_group(GROUP_CODE_PLATFORM_ADMIN):
            raise serializers.ValidationError("You do not have platform admin privileges.")

        # Update last login
        user.last_login = timezone.now()
        user.save(update_fields=["last_login"])

        attrs['user'] = user
        return attrs


class CustomUserProfileSerializer(serializers.ModelSerializer):
    """Serializer for custom user profile."""

    class Meta:
        model = User
        fields = ['first_name', 'last_name', 'primary_dial_code', 'primary_mobile_number', 'secondary_dial_code',
                  'secondary_mobile_number', 'email', 'date_joined', 'last_login']
        read_only_fields = ['primary_dial_code', 'primary_mobile_number', 'email', 'date_joined', 'last_login']


class CuisineCategorySerializer(serializers.ModelSerializer):
    """Serializer for cuisine categories."""

    class Meta:
        model = CuisineCategory
        fields = ['cuisine_category_id', 'name', 'code', 'description', 'order', 'is_active', 'created_at',
                  'updated_at']
        read_only_fields = ['cuisine_category_id', 'code', 'created_at', 'updated_at']


class AdminRestaurantManagementSerializer(serializers.ModelSerializer):
    """Serializer for restaurant approval."""
    admin = serializers.SerializerMethodField()

    class Meta:
        model = Restaurant
        fields = ['restaurant_id', 'name', 'admin', 'dial_code', 'mobile_number', 'email',
                  'minimum_order_quantity', 'is_approved']
        extra_kwargs = {'minimum_order_quantity': {'write_only': True}}

    def get_admin(self, obj) -> str:
        return obj.admin.get_full_name()


class AdminRestaurantBankAccountSerializer(serializers.ModelSerializer):
    """Serializer for restaurant bank accounts."""

    class Meta:
        model = RestaurantBankAccount
        fields = ['account_id', 'bank_name', 'account_holder_name', 'account_type', 'account_number', 'ifsc_code',
                  'created_at', 'updated_at']
        read_only_fields = ['account_id', 'created_at', 'updated_at']


class AdminRestaurantPickUpTimeSerializer(serializers.ModelSerializer):
    """Serializer for restaurant pickup times."""

    class Meta:
        model = RestaurantPickUpTime
        fields = ['pickup_time_id', 'day_of_week', 'start_time', 'end_time', 'created_at', 'updated_at']
        read_only_fields = ['pickup_time_id', 'created_at', 'updated_at']


class AdminRestaurantMenuItemImageSerializer(serializers.ModelSerializer):
    """Serializer for restaurant menu item images."""

    class Meta:
        model = RestaurantMenuItemImage
        fields = ['menu_item_image_id', 'image', 'order']
        read_only_fields = ['menu_item_image_id']


class AdminRestaurantMenuSerializer(serializers.ModelSerializer):
    """Serializer for restaurant menu items."""
    cuisine_category_name = serializers.CharField(source='cuisine_category.name', read_only=True)
    restaurant_name = serializers.CharField(source='restaurant.name', read_only=True)
    images = AdminRestaurantMenuItemImageSerializer(many=True, read_only=True)

    class Meta:
        model = RestaurantMenuItem
        fields = [
            'menu_item_id', 'restaurant', 'restaurant_name', 'cuisine_category', 'cuisine_category_name', 'dish_type',
            'dish_name', 'dish_description', 'images', 'restaurant_price', 'platform_price', 'is_active', 'is_approved',
            'created_at', 'updated_at'
        ]
        read_only_fields = ['menu_item_id', 'restaurant', 'is_approved', 'created_at', 'updated_at']


class AdminRestaurantManagementRetrieveSerializer(serializers.ModelSerializer):
    """Serializer for restaurant approval."""
    admin = CustomUserProfileSerializer(read_only=True)
    pickup_schedule = AdminRestaurantPickUpTimeSerializer(source='pickup_times', many=True, read_only=True)
    account_details = serializers.SerializerMethodField()
    menu_items = serializers.SerializerMethodField()

    class Meta:
        model = Restaurant
        fields = ['restaurant_id', 'name', 'admin', 'dial_code', 'mobile_number', 'email', 'account_details',
                  'website', 'minimum_order_quantity', 'is_approved', 'pickup_schedule', 'menu_items', 'created_at',
                  'updated_at']

    def get_account_details(self, obj) -> dict:
        primary_bank_account = RestaurantBankAccount.objects.filter(restaurant=obj, is_primary=True)
        return AdminRestaurantBankAccountSerializer(primary_bank_account, many=True).data

    def get_menu_items(self, obj) -> dict:
        menu_items = RestaurantMenuItem.objects.filter(restaurant=obj, is_approved=True)
        return AdminRestaurantMenuSerializer(menu_items, many=True).data


class StaticPagesSerializer(serializers.ModelSerializer):
    """Serializer for static pages."""

    class Meta:
        model = StaticPages
        fields = ['page_id', 'slug', 'title', 'is_active', 'content', 'language_code', 'seo_title', 'seo_description']
        read_only_fields = ['page_id', 'created_at', 'updated_at']


class AdminMenuItemApprovalSerializer(serializers.ModelSerializer):
    """Serializer for approving menu items."""

    class Meta:
        model = RestaurantMenuItem
        fields = ['platform_price', 'is_approved']
        read_only_fields = ['is_approved']
        extra_kwargs = {'platform_price': {'required': True}}


class AdminRestaurantPaymentLogSerializer(serializers.ModelSerializer):
    """Serializer for restaurant payment logs."""
    restaurant_name = serializers.CharField(source='restaurant.name', read_only=True)
    bank_account_number = serializers.CharField(source='restaurant_bank_account.account_number', read_only=True)

    class Meta:
        model = RestaurantPaymentLogs
        fields = [
            'payment_log_id', 'payment_ref_id', 'restaurant', 'restaurant_name', 'payment_method',
            'restaurant_bank_account', 'bank_account_number', 'amount', 'payment_receipt', 'payment_remarks',
            'created_at'
        ]
        read_only_fields = ['payment_log_id', 'payment_ref_id', 'created_at']

    def validate(self, attrs):
        """Validate that the payment receipt is a PDF file."""
        payment_method = attrs.get('payment_method')
        restaurant_bank_account = attrs.get('restaurant_bank_account')
        payment_receipt = attrs.get('payment_receipt')

        if payment_method == RestaurantPaymentLogs.BANK_TRANSFER and not restaurant_bank_account:
            raise serializers.ValidationError("Bank account is required for bank transfer.")

        if (payment_method in [RestaurantPaymentLogs.CASH, RestaurantPaymentLogs.UPI]) and restaurant_bank_account:
            raise serializers.ValidationError("Bank account is not required for cash or UPI.")

        if (payment_method in [RestaurantPaymentLogs.BANK_TRANSFER, RestaurantPaymentLogs.UPI]) and not payment_receipt:
            raise serializers.ValidationError("Payment receipt is required for bank transfer and UPI.")

        return attrs


class AdminRestaurantCreateSerializer(serializers.ModelSerializer):
    """Serializer for platform admin to create restaurant with admin."""
    restaurant_name = serializers.CharField(required=True, write_only=True)
    first_name = serializers.CharField(required=True, write_only=True)
    last_name = serializers.CharField(required=True, write_only=True)
    primary_dial_code = serializers.CharField(required=True, write_only=True)
    primary_mobile_number = serializers.CharField(required=True, write_only=True)
    secondary_dial_code = serializers.CharField(required=True, write_only=True)
    secondary_mobile_number = serializers.CharField(required=True, write_only=True)
    admin_email = serializers.EmailField(required=True, write_only=True)

    class Meta:
        model = Restaurant
        fields = ('restaurant_name', 'dial_code', 'mobile_number', 'email', 'first_name', 'last_name',
                  'primary_dial_code', 'primary_mobile_number', 'secondary_dial_code', 'secondary_mobile_number',
                  'admin_email', 'address_line_1', 'address_line_2', 'landmark', 'city', 'state', 'pincode',
                  'minimum_order_quantity')
        extra_kwargs = {
            'dial_code': {'required': True},
            'mobile_number': {'required': True},
            'email': {'required': True},
            'minimum_order_quantity': {'required': False},
        }

    def validate(self, attrs):
        # Check if restaurant admin already exists
        if not unique_restaurant_admin(
                dial_code=attrs['primary_dial_code'],
                mobile_number=attrs['primary_mobile_number'],
                email=attrs['email']
        ):
            raise serializers.ValidationError("A restaurant Admin with this mobile number or email already exists.")

        return attrs

    def create(self, validated_data):
        # Extract user and restaurant data
        restaurant_name = validated_data.pop('restaurant_name')
        first_name = validated_data.pop('first_name')
        last_name = validated_data.pop('last_name')
        dial_code = validated_data.pop('dial_code')
        mobile_number = validated_data.pop('mobile_number')
        primary_dial_code = validated_data.pop('primary_dial_code')
        primary_mobile_number = validated_data.pop('primary_mobile_number')
        secondary_dial_code = validated_data.pop('secondary_dial_code', '')
        secondary_mobile_number = validated_data.pop('secondary_mobile_number', '')
        email = validated_data.pop('email')
        address = validated_data.pop('address', '')
        minimum_order_quantity = validated_data.pop('minimum_order_quantity', 50)
        admin_email = validated_data.pop('admin_email')

        # Generate a strong password
        password = generate_strong_password()

        user, created = User.objects.get_or_create(
            primary_dial_code=primary_dial_code,
            primary_mobile_number=primary_mobile_number,
            defaults={
                'first_name': first_name,
                'last_name': last_name,
                'secondary_dial_code': secondary_dial_code,
                'secondary_mobile_number': secondary_mobile_number,
                'email': admin_email,
                'password': make_password(password),
                'is_active': True,
                'mobile_verified': True  # Auto-verify since admin is creating
            }
        )

        if not created:
            raise serializers.ValidationError("A user with this mobile number already exists.")

        # Add to restaurant admin group
        user.groups.add(restaurant_admin_group())

        # Create restaurant if not exists
        restaurant, created = Restaurant.objects.get_or_create(
            admin=user,
            defaults={
                'name': restaurant_name,
                'address': address,
                'dial_code': dial_code,
                'mobile_number': mobile_number,
                'email': email,
                'minimum_order_quantity': minimum_order_quantity,
                'is_approved': True,  # Auto-approve since platform admin is creating
                'code': generate_restaurant_code()  # Generate restaurant code
            }
        )

        if not created:
            raise serializers.ValidationError("A restaurant with this admin already exists.")

        restaurant = Restaurant.objects.create(
            admin=user,
            name=restaurant_name,
            address=address,
            dial_code=dial_code,
            mobile_number=mobile_number,
            email=email,
            minimum_order_quantity=minimum_order_quantity,
            is_approved=True,  # Auto-approve since platform admin is creating
            code=generate_restaurant_code()  # Generate restaurant code
        )

        # Create default pickup times
        restaurant.create_default_pickup_times()

        # TODO:Send welcome email with credentials
        # send_notification.delay(
        #     user_id=user.user_id,
        #     notification_type=NotificationType.EMAIL.value,
        #     category=NotificationCategory.SYSTEM.value,
        #     template_code=NotificationTemplateCode.RESTAURANT_WELCOME.value,
        #     title="Welcome to Hungersate",
        #     context={
        #         'restaurant_admin_first_name': user.first_name,
        #         'restaurant_admin_last_name': user.last_name,
        #         'restaurant_name': restaurant.name,
        #         'restaurant_login_url': '#',  # Replace with actual URL
        #         'restaurant_admin_mobile': f"{user.primary_dial_code} {user.primary_mobile_number}",
        #         'restaurant_admin_password': password
        #     }
        # )

        return {
            'restaurant': restaurant,
            'admin_credentials': {
                'mobile_number': f"{user.primary_dial_code} {user.primary_mobile_number}",
                'email': user.email,
                'password': password
            }
        }


class FileUploadSerializer(serializers.Serializer):
    """Serializer for uploading multiple files."""
    files = serializers.ListField(
        child=serializers.FileField(
            allow_empty_file=False,
            use_url=False
        ),
        write_only=True
    )

    def validate_files(self, files):
        """Validate files size and type."""
        for file in files:
            # Check file size (10MB limit)
            if file.size > 10 * 1024 * 1024:
                raise serializers.ValidationError(f"File {file.name} is too large. Maximum size is 10MB.")

            # Get file extension
            extension = file.name.split('.')[-1].lower()

            # Check if extension is allowed
            allowed_extensions = ['jpg', 'jpeg', 'png', 'pdf']
            if extension not in allowed_extensions:
                raise serializers.ValidationError(
                    f"{file.name} has an invalid extension. Allowed extensions: {', '.join(allowed_extensions)}"
                )

        return files


class ServiceableStateSerializer(serializers.ModelSerializer):
    """Serializer for serviceable states."""

    class Meta:
        model = ServiceableState
        fields = ['state_id', 'state_code', 'state', 'is_active', 'created_at', 'updated_at']
        read_only_fields = ['state_id', 'created_at', 'updated_at']


class ServiceableCityListSerializer(serializers.ModelSerializer):
    """Serializer for listing serviceable cities."""
    state_name = serializers.CharField(source='state.state', read_only=True)

    class Meta:
        model = ServiceableCity
        fields = ['city_id', 'city_code', 'city', 'state', 'state_name', 'is_active', 'created_at', 'updated_at']
        read_only_fields = ['city_id', 'created_at', 'updated_at']


class ServiceableCityDetailSerializer(serializers.ModelSerializer):
    """Serializer for detailed view of serviceable cities."""
    state_details = ServiceableStateSerializer(source='state', read_only=True)

    class Meta:
        model = ServiceableCity
        fields = ['city_id', 'city_code', 'city', 'state', 'state_details', 'is_active', 'created_at', 'updated_at']
        read_only_fields = ['city_id', 'created_at', 'updated_at']
