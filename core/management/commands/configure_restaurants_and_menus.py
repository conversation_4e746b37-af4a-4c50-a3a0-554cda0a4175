from django.core.management.base import BaseCommand
from django.utils import timezone
from core.models import CustomUser, ServiceableCity
from rbac_system.utils import restaurant_admin_group
from restaurant.models import Restaurant, RestaurantBankAccount, CuisineCategory, RestaurantMenuItem, \
    RestaurantPickUpTime
import random
import json
import os


class Command(BaseCommand):
    help = "Configures restaurants and menus"

    def handle(self, *args, **options):
        self.stdout.write(
            self.style.SUCCESS(
                "\n" + " Starting Restaurant & Their Menus Creation Script ".center(75, "=") + "\n"
            )
        )

        # Assume these cities and cuisine categories exist
        city_codes = ['BLR', 'HYD']
        cuisine_codes = ['indian', 'chinese', 'italian', 'mexican']
        cuisine_categories = {c.code: c for c in CuisineCategory.objects.filter(code__in=cuisine_codes)}
        cities = {c.city_code: c for c in ServiceableCity.objects.filter(city_code__in=city_codes)}

        all_restaurant_data = []

        for i in range(1, 16):
            city_code = city_codes[i % 2]
            city = cities[city_code]
            admin_mobile = f"********{i:02d}"
            admin_email = f"admin@restaurant{i}.com"
            password_plain = f"rest@{i:03d}"
            admin, _ = CustomUser.objects.update_or_create(
                primary_mobile_number=admin_mobile,
                defaults={
                    'first_name': f'RESTADMIN_FIRST_{i}',
                    'last_name': f'RESTADMIN_LAST_{i}',
                    'primary_dial_code': '+91',
                    'email': admin_email,
                    'is_active': True,
                }
            )
            # Set password using set_password to hash it
            admin.set_password(password_plain)
            admin.mobile_verified = True
            admin.groups.set([restaurant_admin_group()])
            admin.save()

            rest_code = f"REST{i:02d}"
            rest_name = f"Sample Restaurant {i}"
            restaurant, created = Restaurant.objects.update_or_create(
                code=rest_code,
                defaults={
                    'admin': admin,
                    'name': rest_name,
                    'description': f"A great place for food {i}",
                    'address': f"{i} Main Street, {city.city}",
                    'dial_code': '+91',
                    'mobile_number': admin_mobile,
                    'email': admin_email,
                    'minimum_order_quantity': 10 + i,
                    'is_approved': True,
                    'registration_number': f"REG{i:04d}",
                    'gst_number': f"GSTIN{i:04d}",
                    'firm_type': 'Private Limited',
                    'owner_name': f"Owner {i}",
                    'owner_dial_code': '+91',
                    'owner_mobile_number': f"80000000{i:02d}",
                    'owner_email': f"owner{i}@restaurant.com",
                    'power_of_attorney_name': f"POA {i}",
                    'power_of_attorney_dial_code': '+91',
                    'power_of_attorney_mobile_number': f"********{i:02d}",
                    'power_of_attorney_email': f"poa{i}@restaurant.com",
                    'point_of_contact_name': f"Contact {i}",
                    'point_of_contact_dial_code': '+91',
                    'point_of_contact_mobile_number': f"********{i:02d}",
                }
            )
            status = "Created" if created else "Updated"
            self.stdout.write(self.style.SUCCESS(f"   ↳ {status} Restaurant: {rest_code} - {rest_name}"))

            # Create/Update Bank Account
            bank_account, _ = RestaurantBankAccount.objects.update_or_create(
                restaurant=restaurant,
                account_number=f"**********{i:02d}",
                defaults={
                    'is_primary': True,
                    'bank_name': f"Bank {i}",
                    'account_holder_name': f"Owner {i}",
                    'account_type': RestaurantBankAccount.SAVING,
                    'ifsc_code': f"IFSC000{i:03d}",
                }
            )
            self.stdout.write(self.style.SUCCESS(f"      ↳ Bank Account set for {rest_name}"))

            # Create/Update Pickup Times (Mon-Sun, 12:00-12:30)
            pickup_times = []
            for day, _ in RestaurantPickUpTime.DAY_OF_WEEK_CHOICES:
                pt, _ = RestaurantPickUpTime.objects.update_or_create(
                    restaurant=restaurant,
                    day_of_week=day,
                    defaults={
                        'start_time': '12:00',
                        'end_time': '12:30',
                    }
                )
                pickup_times.append({
                    'day_of_week': day,
                    'start_time': '12:00',
                    'end_time': '12:30',
                })
            self.stdout.write(self.style.SUCCESS(f"      ↳ Pickup Times set for {rest_name}"))

            # Create/Update Menus (2 per restaurant)
            menu_items = []
            for j in range(12):
                cuisine = cuisine_categories[cuisine_codes[(i + j) % len(cuisine_codes)]]
                dish_type = random.choice([RestaurantMenuItem.VEG, RestaurantMenuItem.NON_VEG])
                dish_name = f"Dish {i}-{j}"
                menu_item, _ = RestaurantMenuItem.objects.update_or_create(
                    restaurant=restaurant,
                    dish_name=dish_name,
                    defaults={
                        'cuisine_category': cuisine,
                        'dish_type': dish_type,
                        'dish_description': f"Tasty {dish_name}",
                        'restaurant_price': 100 + i * 10 + j * 5,
                        'platform_price': 90 + i * 10 + j * 5,
                        'is_approved': True,
                    }
                )
                menu_items.append({
                    'dish_name': dish_name,
                    'cuisine_category': cuisine.name,
                    'dish_type': dish_type,
                    'restaurant_price': float(menu_item.restaurant_price),
                    'platform_price': float(menu_item.platform_price),
                })
                self.stdout.write(self.style.SUCCESS(f"         ↳ Menu Item: {dish_name} ({cuisine.name})"))

            # Print admin credentials
            self.stdout.write(
                self.style.WARNING(f"      ↳ Admin Credentials: Mobile: {admin_mobile}, Password: {password_plain}"))

            # Collect all data for export
            all_restaurant_data.append({
                'restaurant_code': rest_code,
                'restaurant_name': rest_name,
                'admin_mobile': admin_mobile,
                'admin_email': admin_email,
                'admin_password': password_plain,
                'address': f"{i} Main Street, {city.city}",
                'bank_account': {
                    'bank_name': f"Bank {i}",
                    'account_holder_name': f"Owner {i}",
                    'account_type': 'saving',
                    'account_number': f"**********{i:02d}",
                    'ifsc_code': f"IFSC000{i:03d}",
                },
                'pickup_times': pickup_times,
                'menu_items': menu_items,
            })

        # Export to JSON file
        # output_path = os.path.join(os.path.dirname(__file__), 'sample_restaurants.json')
        # with open(output_path, 'w') as f:
        #     json.dump(all_restaurant_data, f, indent=2)
        # self.stdout.write(self.style.SUCCESS(f"\nExported restaurant data to {output_path}\n"))

        self.stdout.write(
            self.style.SUCCESS("\n" + " Restaurant & Their Menus setup complete ".center(75, "=") + "\n")
        )
